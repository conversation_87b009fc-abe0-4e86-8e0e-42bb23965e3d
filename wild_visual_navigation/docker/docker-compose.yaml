services:
  wvn:
    build:
      context: ..
      dockerfile: docker/Dockerfile
      target: dev
    stdin_open: true
    tty: true
    network_mode: "host"
    volumes:
      - ../../wild_visual_navigation:/root/catkin_ws/src/wild_visual_navigation
      - ../../self_supervised_segmentation:/root/catkin_ws/src/self_supervised_segmentation
    environment:
      - ROS_IP=127.0.0.1
    command: "/bin/bash"