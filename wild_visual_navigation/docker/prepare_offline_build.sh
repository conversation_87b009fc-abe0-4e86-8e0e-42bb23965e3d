#!/bin/bash

# ==============================================================================
# Preparation script for completely offline Docker build
# This script downloads all required components to build the Docker image
# without any external dependencies during build time.
# ==============================================================================

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "=== Preparing offline Docker build components ==="

# Create directories
mkdir -p debs ros-debs ros-dev-debs gui-debs python-wheels pydensecrf-source liegroups-source

# ==============================================================================
# 1. Create Ubuntu 20.04 rootfs using debootstrap
# ==============================================================================
echo "Creating Ubuntu 20.04 rootfs..."
if [ ! -f "ubuntu-20.04-rootfs.tar.gz" ]; then
    sudo debootstrap --arch=amd64 focal ubuntu-20.04-rootfs http://archive.ubuntu.com/ubuntu/
    sudo tar -czf ubuntu-20.04-rootfs.tar.gz -C ubuntu-20.04-rootfs .
    sudo rm -rf ubuntu-20.04-rootfs
    echo "Ubuntu rootfs created: ubuntu-20.04-rootfs.tar.gz"
else
    echo "Ubuntu rootfs already exists: ubuntu-20.04-rootfs.tar.gz"
fi

# ==============================================================================
# 2. Download CUDA installer
# ==============================================================================
echo "Downloading CUDA installer..."
if [ ! -f "cuda_12.3.2_545.23.08_linux.run" ]; then
    wget https://developer.download.nvidia.com/compute/cuda/12.3.2/local_installers/cuda_12.3.2_545.23.08_linux.run
    echo "CUDA installer downloaded"
else
    echo "CUDA installer already exists"
fi

# ==============================================================================
# 3. Download Python source
# ==============================================================================
echo "Downloading Python source..."
if [ ! -f "Python-3.8.18.tgz" ]; then
    wget https://www.python.org/ftp/python/3.8.18/Python-3.8.18.tgz
    echo "Python source downloaded"
else
    echo "Python source already exists"
fi

# ==============================================================================
# 4. Download Python wheels
# ==============================================================================
echo "Downloading Python wheels..."
pip download -r requirements.txt -d python-wheels/
echo "Python wheels downloaded to python-wheels/"

# ==============================================================================
# 5. Download source packages that need compilation
# ==============================================================================
echo "Downloading source packages..."

# pydensecrf
if [ ! -d "pydensecrf-source" ] || [ -z "$(ls -A pydensecrf-source)" ]; then
    git clone https://github.com/lucasb-eyer/pydensecrf.git pydensecrf-source
    echo "pydensecrf source downloaded"
fi

# liegroups
if [ ! -d "liegroups-source" ] || [ -z "$(ls -A liegroups-source)" ]; then
    git clone https://github.com/mmattamala/liegroups liegroups-source
    echo "liegroups source downloaded"
fi

# ==============================================================================
# 6. Download system packages
# ==============================================================================
echo "Downloading system packages..."

# Basic system packages
apt-get download \
    ca-certificates curl wget gnupg lsb-release software-properties-common \
    apt-transport-https dirmngr gpg-agent git build-essential cmake nano vim \
    unzip pkg-config libjpeg-dev libpng-dev libtiff-dev libavcodec-dev \
    libavformat-dev libswscale-dev libv4l-dev libxvidcore-dev libx264-dev \
    libgtk-3-dev libatlas-base-dev gfortran

mv *.deb debs/ 2>/dev/null || true

# ROS packages
apt-get download \
    ros-noetic-desktop-full python3-rosdep python3-rosinstall \
    python3-rosinstall-generator python3-wstool python3-catkin-tools \
    python3-osrf-pycommon

mv *.deb ros-debs/ 2>/dev/null || true

# ROS development packages
apt-get download \
    ros-noetic-jackal-simulator ros-noetic-jackal-desktop \
    ros-noetic-teleop-twist-keyboard ros-noetic-rqt-robot-steering

mv *.deb ros-dev-debs/ 2>/dev/null || true

# GUI packages
apt-get download \
    x11-apps mesa-utils libgl1-mesa-glx libgl1-mesa-dri

mv *.deb gui-debs/ 2>/dev/null || true

echo "=== Preparation complete ==="
echo ""
echo "All required files have been downloaded. You can now build the Docker image"
echo "completely offline using:"
echo "  docker build -f Dockerfile -t wvn-offline ."
echo ""
echo "Files created:"
echo "  - ubuntu-20.04-rootfs.tar.gz (Ubuntu base system)"
echo "  - cuda_12.3.2_545.23.08_linux.run (CUDA installer)"
echo "  - Python-3.8.18.tgz (Python source)"
echo "  - python-wheels/ (Python packages)"
echo "  - debs/ (System packages)"
echo "  - ros-debs/ (ROS packages)"
echo "  - ros-dev-debs/ (ROS development packages)"
echo "  - gui-debs/ (GUI packages)"
echo "  - pydensecrf-source/ (pydensecrf source)"
echo "  - liegroups-source/ (liegroups source)"
