# Completely Offline Docker Build

This directory contains a modified Dockerfile that builds the WVN environment completely from scratch without pulling any pre-built images from Docker registries.

## Overview

The modified Dockerfile (`Dockerfile`) creates a completely self-contained Docker image by:

1. Starting from `scratch` (no base image)
2. Adding a pre-built Ubuntu 20.04 rootfs
3. Installing CUDA runtime from local installer
4. Building Python from source
5. Installing all ROS packages from local .deb files
6. Installing Python packages from local wheels
7. Building additional packages from source

## Prerequisites

Before building the Docker image, you need to prepare all the required components offline. This ensures no network access is needed during the build process.

## Preparation Steps

1. **Make the preparation script executable:**
   ```bash
   chmod +x prepare_offline_build.sh
   ```

2. **Run the preparation script:**
   ```bash
   ./prepare_offline_build.sh
   ```

   This script will:
   - Create Ubuntu 20.04 rootfs using debootstrap
   - Download CUDA installer
   - Download Python source code
   - Download all Python wheels
   - Download all system .deb packages
   - Clone source repositories for packages that need compilation

3. **Verify all files are downloaded:**
   After running the script, you should have:
   ```
   docker/
   ├── ubuntu-20.04-rootfs.tar.gz
   ├── cuda_12.3.2_545.23.08_linux.run
   ├── Python-3.8.18.tgz
   ├── python-wheels/
   ├── debs/
   ├── ros-debs/
   ├── ros-dev-debs/
   ├── gui-debs/
   ├── pydensecrf-source/
   └── liegroups-source/
   ```

## Building the Image

Once all components are prepared, build the Docker image:

```bash
# Build base image
docker build -f Dockerfile --target base -t wvn-offline-base .

# Build development image
docker build -f Dockerfile --target dev -t wvn-offline-dev .
```

## Key Differences from Original

1. **No external dependencies:** The build process doesn't require internet access
2. **From scratch:** Starts with `FROM scratch` instead of a pre-built NVIDIA image
3. **Local packages:** All packages are installed from local files
4. **Self-contained:** Everything needed is included in the build context

## File Structure

- `Dockerfile` - Modified Dockerfile for offline build
- `prepare_offline_build.sh` - Script to download all required components
- `requirements.txt` - Python package requirements
- `ubuntu-20.04-rootfs.tar.gz` - Ubuntu base system (created by script)
- `cuda_12.3.2_545.23.08_linux.run` - CUDA installer (downloaded by script)
- `Python-3.8.18.tgz` - Python source (downloaded by script)
- `python-wheels/` - Python package wheels (downloaded by script)
- `debs/` - System .deb packages (downloaded by script)
- `ros-debs/` - ROS .deb packages (downloaded by script)
- `ros-dev-debs/` - ROS development .deb packages (downloaded by script)
- `gui-debs/` - GUI .deb packages (downloaded by script)
- `pydensecrf-source/` - pydensecrf source code (cloned by script)
- `liegroups-source/` - liegroups source code (cloned by script)

## Notes

- The preparation script requires `sudo` access for creating the Ubuntu rootfs
- The total size of all downloaded components will be several GB
- The build process will take significantly longer than the original due to compiling from source
- This approach ensures complete independence from external Docker registries

## Troubleshooting

If the build fails:

1. Ensure all files were downloaded correctly by the preparation script
2. Check that you have sufficient disk space (>10GB recommended)
3. Verify that Docker has enough resources allocated
4. Check the build logs for specific error messages

## Security Benefits

This approach provides several security benefits:

1. **Supply chain security:** No dependency on external Docker images
2. **Reproducible builds:** All components are versioned and local
3. **Air-gapped deployment:** Can be built in environments without internet access
4. **Audit trail:** All components can be inspected before building
