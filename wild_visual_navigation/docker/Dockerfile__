#####################
# Base image #
#####################
FROM ubuntu:20.04 as base

# Labels
LABEL maintainer="<PERSON><PERSON>;<PERSON>"
LABEL contact="<EMAIL>;j<PERSON><PERSON>@ethz.ch"
LABEL description="WVN Docker - Self-contained build"

ARG ROS_VERSION="noetic"
ARG CUDA_VERSION="12.3"
ARG CUDA_PKG_VERSION="12-3"

# ==
# Disable dialog frontend
# ==
ARG DEBIAN_FRONTEND=noninteractive

# ==
# Select shell
# ==
SHELL ["/bin/bash", "-c"]

# ==
# Install basic system dependencies
# ==
RUN apt-get update \
    && apt-get install --no-install-recommends -y \
        ca-certificates \
        curl \
        wget \
        gnupg \
        lsb-release \
        software-properties-common \
        apt-transport-https \
        dirmngr \
        gpg-agent \
    && rm -rf /var/lib/apt/lists/*

# ==
# Install CUDA runtime manually
# ==
RUN wget https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2004/x86_64/cuda-keyring_1.0-1_all.deb \
    && dpkg -i cuda-keyring_1.0-1_all.deb \
    && rm cuda-keyring_1.0-1_all.deb \
    && apt-get update \
    && apt-get install --no-install-recommends -y \
        cuda-runtime-${CUDA_PKG_VERSION} \
        cuda-cudart-${CUDA_PKG_VERSION} \
        cuda-compat-${CUDA_PKG_VERSION} \
        libnvidia-compute-535 \
        libnvidia-decode-535 \
        libnvidia-encode-535 \
        nvidia-driver-535 \
    && rm -rf /var/lib/apt/lists/*

# ==
# Set CUDA environment variables
# ==
ENV CUDA_HOME=/usr/local/cuda-${CUDA_VERSION}
ENV PATH=${CUDA_HOME}/bin:${PATH}
ENV LD_LIBRARY_PATH=${CUDA_HOME}/lib64:${LD_LIBRARY_PATH}
ENV NVIDIA_VISIBLE_DEVICES=all
ENV NVIDIA_DRIVER_CAPABILITIES=compute,utility

# ==
# Install essential build tools and dependencies
# ==
RUN apt-get update \
    && apt-get install --no-install-recommends -y \
        git \
        build-essential \
        cmake \
        nano \
        vim \
        unzip \
        pkg-config \
        libjpeg-dev \
        libpng-dev \
        libtiff-dev \
        libavcodec-dev \
        libavformat-dev \
        libswscale-dev \
        libv4l-dev \
        libxvidcore-dev \
        libx264-dev \
        libgtk-3-dev \
        libatlas-base-dev \
        gfortran \
        python3 \
        python3-dev \
        python3-pip \
        python3-venv \
        python3-setuptools \
        python3-wheel \
    && rm -rf /var/lib/apt/lists/*

# ==
# Install ROS Noetic manually
# ==
RUN sh -c 'echo "deb http://packages.ros.org/ros/ubuntu $(lsb_release -sc) main" > /etc/apt/sources.list.d/ros-latest.list' \
    && curl -s https://raw.githubusercontent.com/ros/rosdistro/master/ros.asc | apt-key add - \
    && apt-get update \
    && apt-get install --no-install-recommends -y \
        ros-${ROS_VERSION}-desktop-full \
        python3-rosdep \
        python3-rosinstall \
        python3-rosinstall-generator \
        python3-wstool \
        python3-catkin-tools \
        python3-osrf-pycommon \
    && rm -f "/etc/ros/rosdep/sources.list.d/20-default.list" \
    && rosdep init \
    && rosdep update \
    && echo "source /opt/ros/noetic/setup.bash" >> ~/.bashrc \
    && rm -rf /var/lib/apt/lists/*

# ==
# Install Python packages
# ==
RUN cd /root \
    && python3 -m venv env --system-site-packages \
    && source /root/env/bin/activate \
    && pip install --upgrade pip setuptools wheel \
    && pip3 install --no-cache-dir \
         torch==2.1.0 torchvision --extra-index-url https://download.pytorch.org/whl/cu121 \
    && pip3 install --no-cache-dir \
         black \
         flake8 \
         jupyter \
         wget \
         numpy \
         tqdm \
         kornia \
         torchmetrics \
         pytorch_lightning \
         pytest \
         scipy \
         scikit-image \
         scikit-learn \
         matplotlib \
         seaborn \
         pandas \
         pytictac \
         torch_geometric \
         omegaconf \
         optuna \
         neptune \
         fast-slic \
         hydra-core \
         prettytable \
         termcolor \
         opencv-python \
    && pip3 install --no-cache-dir \
         pydensecrf@git+https://github.com/lucasb-eyer/pydensecrf.git \
         liegroups@git+https://github.com/mmattamala/liegroups

# ==
# Clean up package caches and temporary files
# ==
RUN apt-get autoremove -y \
    && apt-get autoclean \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/* \
    && pip cache purge


# ==
# Enable dialog again
# ==
ARG DEBIAN_FRONTEND=dialog

# ==
# Run bash
# ==
CMD ["/bin/bash"]
WORKDIR /root/catkin_ws


###############################################################
# Development image
# This adds display access and simulation support
###############################################################

FROM base as dev

ENV DEBIAN_FRONTEND=noninteractive

# ==
# Install additional ROS packages for simulation and development
# ==
RUN apt-get update \
    && apt-get install --no-install-recommends -y \
        ros-noetic-jackal-simulator \
        ros-noetic-jackal-desktop \
        ros-noetic-teleop-twist-keyboard \
        ros-noetic-rqt-robot-steering \
        x11-apps \
        mesa-utils \
        libgl1-mesa-glx \
        libgl1-mesa-dri \
    && rm -rf /var/lib/apt/lists/*

# ==
# Setup catkin workspace
# ==
RUN mkdir -p /root/catkin_ws/src \
    && source /opt/ros/noetic/setup.bash \
    && source "/root/.bashrc" \
    && cd /root/catkin_ws && catkin build

# ==
# Configure environment
# ==
RUN echo "source /root/catkin_ws/devel/setup.bash" >> ~/.bashrc \
    && echo "source /root/env/bin/activate" >> ~/.bashrc

# ==
# Copy setup script
# ==
COPY docker/first_run.sh first_run.sh
RUN chmod +x first_run.sh

ENV DEBIAN_FRONTEND=dialog