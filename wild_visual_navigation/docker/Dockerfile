# ==============================================================================
# COMPLETELY SELF-CONTAINED DOCKERFILE - NO EXTERNAL IMAGES
# ==============================================================================
#
# This Dockerfile builds everything from scratch without pulling any images
# from Docker registries. It requires pre-built Ubuntu rootfs and packages.
#
# Prerequisites:
# 1. Create ubuntu-20.04-rootfs.tar.gz using debootstrap
# 2. Download all required .deb packages locally
# 3. Download CUDA installer
# ==============================================================================

FROM scratch as base

# Labels
LABEL maintainer="<PERSON><PERSON>la;<PERSON>"
LABEL contact="<EMAIL>;<EMAIL>"
LABEL description="WVN Docker - Complete self-contained build from scratch"

# Add the pre-built Ubuntu 20.04 rootfs
# This rootfs should be created using:
# debootstrap --arch=amd64 focal ubuntu-20.04-rootfs http://archive.ubuntu.com/ubuntu/
# tar -czf ubuntu-20.04-rootfs.tar.gz -C ubuntu-20.04-rootfs .
ADD ubuntu-20.04-rootfs.tar.gz /

# Set basic environment
ENV PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
ENV DEBIAN_FRONTEND=noninteractive
ENV TERM=xterm
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8

# Set ROS and CUDA versions
ENV ROS_VERSION=noetic
ENV CUDA_VERSION=12.3
ENV CUDA_PKG_VERSION=12-3

# Configure basic system
RUN echo 'APT::Get::Assume-Yes "true";' > /etc/apt/apt.conf.d/90assumeyes \
    && echo 'APT::Get::Install-Recommends "false";' > /etc/apt/apt.conf.d/90norecommends

# Set shell
SHELL ["/bin/bash", "-c"]

# ==
# Install local .deb packages (pre-downloaded)
# Copy all required .deb files to /tmp/debs/ directory before building
# ==
COPY debs/ /tmp/debs/
RUN dpkg -i /tmp/debs/*.deb || true \
    && apt-get -f install \
    && rm -rf /tmp/debs

# ==
# Install CUDA runtime from local installer
# Download CUDA installer locally first:
# wget https://developer.download.nvidia.com/compute/cuda/12.3.2/local_installers/cuda_12.3.2_545.23.08_linux.run
# ==
COPY cuda_12.3.2_545.23.08_linux.run /tmp/
RUN chmod +x /tmp/cuda_12.3.2_545.23.08_linux.run \
    && /tmp/cuda_12.3.2_545.23.08_linux.run --silent --toolkit --no-opengl-libs \
    && rm /tmp/cuda_12.3.2_545.23.08_linux.run

# ==
# Set CUDA environment variables
# ==
ENV CUDA_HOME=/usr/local/cuda-${CUDA_VERSION}
ENV PATH=${CUDA_HOME}/bin:${PATH}
ENV LD_LIBRARY_PATH=${CUDA_HOME}/lib64:${LD_LIBRARY_PATH}
ENV NVIDIA_VISIBLE_DEVICES=all
ENV NVIDIA_DRIVER_CAPABILITIES=compute,utility

# ==
# Install Python from source (completely self-contained)
# ==
COPY Python-3.8.18.tgz /tmp/
RUN cd /tmp \
    && tar -xzf Python-3.8.18.tgz \
    && cd Python-3.8.18 \
    && ./configure --enable-optimizations --with-ensurepip=install \
    && make -j$(nproc) \
    && make altinstall \
    && ln -sf /usr/local/bin/python3.8 /usr/local/bin/python3 \
    && ln -sf /usr/local/bin/pip3.8 /usr/local/bin/pip3 \
    && cd / && rm -rf /tmp/Python-3.8.18*

# ==
# Install ROS Noetic from local packages
# All ROS .deb packages should be pre-downloaded and placed in ros-debs/
# ==
COPY ros-debs/ /tmp/ros-debs/
RUN dpkg -i /tmp/ros-debs/*.deb || true \
    && apt-get -f install \
    && rm -rf /tmp/ros-debs \
    && echo "source /opt/ros/noetic/setup.bash" >> ~/.bashrc

# ==
# Install Python packages from local wheels
# Pre-download all wheels using: pip download -r requirements.txt -d python-wheels/
# ==
COPY python-wheels/ /tmp/python-wheels/
COPY requirements.txt /tmp/

RUN cd /root \
    && python3 -m venv env --system-site-packages \
    && source /root/env/bin/activate \
    && pip install --upgrade pip setuptools wheel \
    && pip install --no-index --find-links /tmp/python-wheels/ -r /tmp/requirements.txt \
    && rm -rf /tmp/python-wheels /tmp/requirements.txt

# ==
# Install additional packages from source (for packages not available as wheels)
# ==
COPY pydensecrf-source/ /tmp/pydensecrf-source/
COPY liegroups-source/ /tmp/liegroups-source/

RUN source /root/env/bin/activate \
    && cd /tmp/pydensecrf-source && pip install . \
    && cd /tmp/liegroups-source && pip install . \
    && rm -rf /tmp/pydensecrf-source /tmp/liegroups-source

# ==
# Clean up temporary files
# ==
RUN rm -rf /tmp/* \
    && rm -rf /var/tmp/* \
    && find /root/env -name "*.pyc" -delete \
    && find /root/env -name "__pycache__" -type d -exec rm -rf {} + || true


# ==
# Enable dialog again
# ==
ARG DEBIAN_FRONTEND=dialog

# ==
# Run bash
# ==
CMD ["/bin/bash"]
WORKDIR /root/catkin_ws


###############################################################
# Development image
# This adds display access and simulation support
###############################################################

FROM base as dev

ENV DEBIAN_FRONTEND=noninteractive

# ==
# Install additional ROS packages from local debs
# ==
COPY ros-dev-debs/ /tmp/ros-dev-debs/
RUN dpkg -i /tmp/ros-dev-debs/*.deb || true \
    && apt-get -f install \
    && rm -rf /tmp/ros-dev-debs

# ==
# Install GUI and graphics libraries from local debs
# ==
COPY gui-debs/ /tmp/gui-debs/
RUN dpkg -i /tmp/gui-debs/*.deb || true \
    && apt-get -f install \
    && rm -rf /tmp/gui-debs

# ==
# Setup catkin workspace
# ==
RUN mkdir -p /root/catkin_ws/src \
    && source /opt/ros/noetic/setup.bash \
    && source "/root/.bashrc" \
    && cd /root/catkin_ws && catkin build

# ==
# Configure environment
# ==
RUN echo "source /root/catkin_ws/devel/setup.bash" >> ~/.bashrc \
    && echo "source /root/env/bin/activate" >> ~/.bashrc

# ==
# Copy setup script
# ==
COPY docker/first_run.sh first_run.sh
RUN chmod +x first_run.sh

ENV DEBIAN_FRONTEND=dialog