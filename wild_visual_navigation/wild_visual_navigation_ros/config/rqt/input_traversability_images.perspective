{"keys": {}, "groups": {"mainwindow": {"keys": {"geometry": {"repr(QByteArray.hex)": "QtCore.QByteArray(b'01d9d0cb00030000000007c80000001b00000eff00000437000007c80000004000000eff0000043700000000000000000780000007c80000004000000eff00000437')", "type": "repr(QByteArray.hex)", "pretty-print": "           7   @   7        @   7"}, "state": {"repr(QByteArray.hex)": "QtCore.QByteArray(b'000000ff00000000fd000000010000000300000738000003ccfc0100000001fc0000000000000738000001aa00fffffffc0100000002fb0000005a007200710074005f0069006d006100670065005f0076006900650077005f005f0049006d0061006700650056006900650077005f005f0032005f005f0049006d00610067006500560069006500770057006900640067006500740100000000000003b9000000dd00fffffffb0000005a007200710074005f0069006d006100670065005f0076006900650077005f005f0049006d0061006700650056006900650077005f005f0031005f005f0049006d006100670065005600690065007700570069006400670065007401000003bf00000379000000c700ffffff000007380000000000000004000000040000000800000008fc00000001000000030000000100000036004d0069006e0069006d0069007a006500640044006f0063006b00570069006400670065007400730054006f006f006c0062006100720000000000ffffffff0000000000000000')", "type": "repr(QByteArray.hex)", "pretty-print": "                   8                                                                                                                  y     8                                                      "}}, "groups": {"toolbar_areas": {"keys": {"MinimizedDockWidgetsToolbar": {"repr": "8", "type": "repr"}}, "groups": {}}}}, "pluginmanager": {"keys": {"running-plugins": {"repr": "{'rqt_image_view/ImageView': [1, 2]}", "type": "repr"}}, "groups": {"plugin__rqt_image_view__ImageView__1": {"keys": {}, "groups": {"dock_widget__ImageViewWidget": {"keys": {"dock_widget_title": {"repr": "'Image View'", "type": "repr"}, "dockable": {"repr": "True", "type": "repr"}, "parent": {"repr": "None", "type": "repr"}}, "groups": {}}, "plugin": {"keys": {"dynamic_range": {"repr": "False", "type": "repr"}, "max_range": {"repr": "10.0", "type": "repr"}, "mouse_pub_topic": {"repr": "'/wild_visual_navigation_node/front/traversability_mouse_left'", "type": "repr"}, "num_gridlines": {"repr": "0", "type": "repr"}, "publish_click_location": {"repr": "False", "type": "repr"}, "rotate": {"repr": "0", "type": "repr"}, "smooth_image": {"repr": "False", "type": "repr"}, "toolbar_hidden": {"repr": "False", "type": "repr"}, "topic": {"repr": "'/wild_visual_navigation_node/front/traversability'", "type": "repr"}, "zoom1": {"repr": "False", "type": "repr"}}, "groups": {}}}}, "plugin__rqt_image_view__ImageView__2": {"keys": {}, "groups": {"dock_widget__ImageViewWidget": {"keys": {"dock_widget_title": {"repr": "'Image View (2)'", "type": "repr"}, "dockable": {"repr": "True", "type": "repr"}, "parent": {"repr": "None", "type": "repr"}}, "groups": {}}, "plugin": {"keys": {"dynamic_range": {"repr": "False", "type": "repr"}, "max_range": {"repr": "10.0", "type": "repr"}, "mouse_pub_topic": {"repr": "'/wild_visual_navigation_node/front/image_input_mouse_left'", "type": "repr"}, "num_gridlines": {"repr": "0", "type": "repr"}, "publish_click_location": {"repr": "False", "type": "repr"}, "rotate": {"repr": "0", "type": "repr"}, "smooth_image": {"repr": "False", "type": "repr"}, "toolbar_hidden": {"repr": "False", "type": "repr"}, "topic": {"repr": "'/wild_visual_navigation_node/front/image_input'", "type": "repr"}, "zoom1": {"repr": "False", "type": "repr"}}, "groups": {}}}}}}}}