#!/bin/env python
# This node implements a simple data logging pipeline
# that subscribes to important topics generated by WVN
# for offline analysis, such as images and learning curves

from sensor_msgs.msg import Image
from wild_visual_navigation_msgs.msg import SystemState
from cv_bridge import CvBridge

import csv
import cv2
import os
import rospy
import rospkg
import yaml


def secs_to_str(secs):
    SECONDS_DIGITS = 10
    secs_str = str(secs)
    return secs_str.zfill(SECONDS_DIGITS)


def nsecs_to_str(nsecs):
    NSECONDS_DIGITS = 9
    nsecs_str = str(nsecs)
    return nsecs_str.zfill(NSECONDS_DIGITS)


class OfflineLogger:
    def __init__(self):
        # Read parameters
        self._read_params()

        # Initialize variables
        self._bridge = CvBridge()

        package_path = rospkg.RosPack().get_path("wild_visual_navigation_ros")
        base_output_path = os.path.join(package_path, "output")

        # Remove old checkpoint
        state_dict = os.path.join(package_path, "../.tmp_state_dict.pt")
        if os.path.exists(state_dict):
            os.remove(state_dict)

        # Initialize log folder
        run = 0
        while True:
            mission_name = f"{self._mission_name}_{str(run).zfill(2)}"
            self._output_path = os.path.join(base_output_path, mission_name)

            if os.path.exists(self._output_path):
                run += 1
            else:
                break

        # Make folder
        mission_name = f"{self._mission_name}_{str(run).zfill(2)}"
        self._output_path = os.path.join(base_output_path, mission_name)
        os.makedirs(self._output_path, exist_ok=False)

        # Initialize CSV writer
        csv_file = open(f"{self._output_path}/wvn_state.csv", "w")
        self._csv_writer = csv.writer(
            csv_file,
            delimiter=",",
            quotechar="|",
            quoting=csv.QUOTE_MINIMAL,
        )
        # write header
        self._csv_writer.writerow(
            [
                "stamp",
                "mode",
                "mission_graph_node_num_valid_node",
                "loss_total",
                "loss_trav",
                "loss_reco",
                "step",
                "pause_learning",
                "scale_traversability_threshold",
            ]
        )

        # Dump parameters
        with open(f"{self._output_path}/wvn_feature_extractor_params.yaml", "w") as outfile:
            yaml.dump(rospy.get_param("/wvn_feature_extractor_node"), outfile, default_flow_style=False)

        # Initialize subscribers
        self._image_sub = rospy.Subscriber(
            self._image_topic, Image, self._image_callback, callback_args="image", queue_size=1
        )
        self._trav_sub = rospy.Subscriber(
            self._trav_topic, Image, self._image_callback, callback_args="trav", queue_size=1
        )
        self._state_sub = rospy.Subscriber(self._state_topic, SystemState, self._state_callback, queue_size=1)

    def _read_params(self):
        self._mission_name = rospy.get_param("~mission_name", "default")
        self._image_topic = rospy.get_param("~image_topic", "/wild_visual_navigation_node/front/image_input")
        self._trav_topic = rospy.get_param(
            "~trav_topic", "/wild_visual_navigation_visu_traversability/traversability_overlayed"
        )
        self._state_topic = rospy.get_param("~state_topic", "/wild_visual_navigation_node/system_state")

        rospy.loginfo(f"Logging mission [{self._mission_name}]")

    def _image_callback(self, image_msg, source):
        cv_img = self._bridge.imgmsg_to_cv2(image_msg, desired_encoding="bgr8")
        secs = image_msg.header.stamp.secs
        nsecs = image_msg.header.stamp.nsecs

        filename = f"{self._output_path}/{source}_{secs_to_str(secs)}_{nsecs_to_str(nsecs)}.png"
        cv2.imwrite(filename, cv_img)

    def _state_callback(self, system_state_msg):
        stamp = rospy.get_rostime()
        self._csv_writer.writerow(
            [
                f"{secs_to_str(stamp.secs)}.{nsecs_to_str(stamp.nsecs)}",
                system_state_msg.mode,
                system_state_msg.mission_graph_num_valid_node,
                system_state_msg.loss_total,
                system_state_msg.loss_trav,
                system_state_msg.loss_reco,
                system_state_msg.step,
                system_state_msg.pause_learning,
                system_state_msg.scale_traversability_threshold,
            ]
        )


if __name__ == "__main__":
    rospy.init_node("wild_visual_navigation_postprocess_logger")
    wvn = OfflineLogger()
    rospy.spin()
