# .pre-commit-config.yaml
# From https://sbarnea.com/lint/black/
---
repos:
  - repo: https://github.com/python/black.git
    rev: 22.12.0
    hooks:
      - id: black
        language_version: python3
  - repo: https://github.com/pycqa/flake8
    rev: 3.7.9
    hooks:
      - id: flake8
        additional_dependencies:
          - flake8-black>=0.1.1
        language_version: python3
  - repo: local
    hooks:
      - id: jupyter-nb-clear-output
        name: jupyter-nb-clear-output
        files: \.ipynb$
        stages: [commit]
        language: system
        entry: jupyter nbconvert --ClearOutputPreprocessor.enabled=True --inplace
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0 # Use the version you want
    hooks:
      - id: check-added-large-files
        args: ["--maxkb=2000"]