<?xml version="1.0" encoding="utf-8"?>
<launch>
    <!-- Default simulation args -->
    <arg name="use_sim_time" default="true" />
    <arg name="gui" default="false" />
    <arg name="headless" default="false" />
    <arg name="world_name" default="$(find wild_visual_navigation_jackal)/worlds/outdoor.world" />
    
    <arg name="x" default="0.0" />
    <arg name="y" default="5.0" />
    <arg name="z" default="0.0" />
    <arg name="yaw" default="0" />
  
    <!-- Launch Gazebo with the specified world -->
    <include file="$(find gazebo_ros)/launch/empty_world.launch">
      <arg name="verbose" value="true" />
      <arg name="gui" value="$(arg gui)" />
      <arg name="use_sim_time" value="$(arg use_sim_time)" />
      <arg name="headless" value="$(arg headless)" />
      <arg name="world_name" value="$(arg world_name)" />
    </include>
  
  <!-- <PERSON>ad Jackal's description, controllers, and spawn-->
  <param name="robot_description"
         command="$(find wild_visual_navigation_jackal)/urdf/load_xacro.sh" />
  <node name="robot_state_publisher" pkg="robot_state_publisher" type="robot_state_publisher" />
  
  <include file="$(find jackal_control)/launch/control.launch"/>

  <node name="urdf_spawner" pkg="gazebo_ros" type="spawn_model"
	      args="-urdf -model jackal -param robot_description -x $(arg x) -y $(arg y) -z $(arg z) -R 0 -P 0 -Y $(arg yaw)" />
  
  <!-- Simulation demo helpers -->
  <node name="gazebo_world_publisher" pkg="wild_visual_navigation_jackal" type="gazebo_world_publisher.py"/>
  <node name="jackal_carrot_follower" pkg="wild_visual_navigation_jackal" type="carrot_follower.py"/>

  <!-- RViz window -->
  <include file="$(find wild_visual_navigation_jackal)/launch/view.launch"/>

</launch>
