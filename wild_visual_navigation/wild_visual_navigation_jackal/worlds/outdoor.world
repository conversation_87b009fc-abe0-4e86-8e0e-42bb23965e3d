<?xml version="1.0"?>
<sdf version='1.6'>
  <world name='outdoor'>
    <scene>
      <ambient>0.2 0.2 0.2 1</ambient>
      <shadows>1</shadows>
      <grid>0</grid>
    </scene>
    <gravity>0 0 -9.81</gravity>
    <light name='sun' type='directional'>
      <cast_shadows>1</cast_shadows>
      <pose frame=''>0 0 100 0 -0 0</pose>
      <diffuse>0.8 0.8 0.8 1</diffuse>
      <specular>0.9 0.9 0.9 1</specular>
      <attenuation>
        <range>1000</range>
        <constant>0.1</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
      <direction>0.2 0.2 -0.9</direction>
    </light>
    <model name="terrain">
		<static>true</static>
		<link name="terrain_link">
			<pose>0 0 0 0 0 0</pose>
			<collision name="terrain_collision">
				<geometry>
					<mesh>
						<uri>file://outdoor.dae</uri>
						<scale>1 1 1</scale>
					</mesh>
				</geometry>
			</collision>
			<visual name="terrain_visual">
				<geometry>
					<mesh>
						<uri>file://outdoor.dae</uri>
						<scale>1 1 1</scale>
					</mesh>
				</geometry>
				<cast_shadows>false</cast_shadows>
			</visual>
		</link>
	</model>
  </world>
</sdf>