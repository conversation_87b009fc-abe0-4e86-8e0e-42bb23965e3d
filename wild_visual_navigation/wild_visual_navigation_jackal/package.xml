<?xml version="1.0"?>
<package format="2">
  <name>wild_visual_navigation_jackal</name>
  <version>0.0.1</version>
  <description>Jackal simulation environment for wild_visual_navigation</description>
  <maintainer email="<EMAIL>"><PERSON><PERSON></maintainer>
  <maintainer email="j<PERSON><PERSON>@ethz.ch"><PERSON></maintainer>
  <author email="<EMAIL>"><PERSON><PERSON></author>
  <author email="<EMAIL>"><PERSON></author>

  <license>Proprietary</license>

  <buildtool_depend>catkin</buildtool_depend>

  <depend>rospy</depend>
  <depend>nav_msgs</depend>
  <depend>sensor_msgs</depend>
  <depend>std_msgs</depend>
  <depend>wild_visual_navigation_ros</depend>

  <exec_depend>gazebo_ros</exec_depend>
  <export>
    <gazebo_ros gazebo_media_path="${prefix}"/>
    <gazebo_ros gazebo_model_path="${prefix}/models"/>
  </export>
</package>
