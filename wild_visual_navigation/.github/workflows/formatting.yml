name: Formatting
on: [push, pull_request]
jobs:
  check-formatting:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v1
      - name: Black Code Formatter
        uses: lgeiger/black-action@master
        with:
          args: "--line-length 120 . --check"
          name: black-action

  auto-formatting:
    name: runner / black
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Check files using the black formatter
        uses: rickstaa/action-black@v1
        id: action_black
        with:
          black_args: "--line-length 120 ."
      # - name: Create Pull Request
      #   if: steps.action_black.outputs.is_formatted == 'true'
      #   uses: peter-evans/create-pull-request@v3
      #   with:
      #     token: ${{ secrets.GITHUB_TOKEN }}
      #     title: "Format Python code with psf/black push"
      #     commit-message: ":art: Format Python code with psf/black"
      #     body: |
      #       There appear to be some python formatting errors in ${{ github.sha }}. This pull request
      #       uses the [psf/black](https://github.com/psf/black) formatter to fix these issues.
      #     base: ${{ github.head_ref }} # Creates pull request onto pull request or commit branch
      #     branch: actions/black