<?xml version="1.0"?>
<launch>
    <arg name="map_frame"                     default="map"/>
    <arg name="base_frame"                    default="base"/>
    
    <node name="vtr_teach_trajectory_server1" pkg="hector_trajectory_server" type="hector_trajectory_server" output="screen">
        <remap from="/trajectory"             to="/gps_trajectory1"/>
        <param name="target_frame_name"       type="string" value="$(arg map_frame)" />
        <param name="source_frame_name"       type="string" value="$(arg base_frame)" />
        <param name="trajectory_update_rate"  type="double" value="10" />
        <param name="trajectory_publish_rate" type="double" value="10" />
    </node>
</launch>